import { PermissionEnum } from '@/shared/types/permission';

// Giao diện cho ModernMenuItem với trường permission
export interface ModernMenuItem {
  id: string;
  label: string;
  path: string;
  icon: string;
  keywords: string[];
  permission: PermissionEnum;
}

/**
 * <PERSON>h sách menu cho người dùng thông thường
 * Chỉ chứa các module có trong HomePage
 */
export const userMenuItems: ModernMenuItem[] = [
  {
    id: 'home',
    label: 'common:home',
    path: '/',
    icon: 'home',
    keywords: ['trang chủ', 'home', 'main', 'dashboard', 'trang chinh'],
    permission: PermissionEnum.DASHBOARD_VIEW_OWN,
  },
  {
    id: 'okrs',
    label: 'OKRs',
    path: '/okrs',
    icon: 'award',
    keywords: [
      'okrs',
      'mục tiêu',
      'muc tieu',
      'objectives',
      'key results',
      'kết quả then chốt',
      'ket qua then chot',
    ],
    permission: PermissionEnum.DASHBOARD_VIEW_OWN, // Sử dụng quyền cơ bản cho OKR
  },
  {
    id: 'todolist',
    label: 'Todolist',
    path: '/todolist',
    icon: 'check',
    keywords: [
      'todolist',
      'công việc',
      'cong viec',
      'dự án',
      'du an',
      'tasks',
      'todo',
      'việc cần làm',
      'viec can lam',
    ],
    permission: PermissionEnum.PROJECT_VIEW_LIST,
  },
  {
    id: 'calendar',
    label: 'home:modules.calendar.title',
    path: '/calendar',
    icon: 'calendar',
    keywords: [
      'calendar',
      'lịch',
      'lich',
      'lịch làm việc',
      'lich lam viec',
      'sự kiện',
      'su kien',
      'events',
    ],
    permission: PermissionEnum.DASHBOARD_VIEW_OWN, // Sử dụng quyền cơ bản cho calendar
  },
  {
    id: 'marketing',
    label: 'Marketing',
    path: '/marketing',
    icon: 'campaign',
    keywords: [
      'marketing',
      'chiến dịch',
      'chien dich',
      'campaign',
      'quảng cáo',
      'quang cao',
      'nội dung',
      'noi dung',
    ],
    permission: PermissionEnum.DASHBOARD_VIEW_OWN, // Sử dụng quyền cơ bản cho marketing
  },
  {
    id: 'crm',
    label: 'CRM',
    path: '/crm',
    icon: 'users',
    keywords: [
      'crm',
      'khách hàng',
      'khach hang',
      'customers',
      'clients',
      'mối quan hệ',
      'moi quan he',
      'relationships',
    ],
    permission: PermissionEnum.CUSTOMER_VIEW_LIST,
  },
  {
    id: 'hrm',
    label: 'HRM',
    path: '/hrm',
    icon: 'user',
    keywords: [
      'hrm',
      'nhân sự',
      'nhan su',
      'human resources',
      'tuyển dụng',
      'tuyen dung',
      'nhân viên',
      'nhan vien',
      'employees',
    ],
    permission: PermissionEnum.EMPLOYEE_VIEW_LIST,
  },
  {
    id: 'integrations',
    label: 'Tích hợp',
    path: '/integrations',
    icon: 'integration',
    keywords: [
      'integrations',
      'tích hợp',
      'tich hop',
      'kết nối',
      'ket noi',
      'api',
      'webhook',
      'third party',
      'bên thứ ba',
      'ben thu ba',
    ],
    permission: PermissionEnum.EMPLOYEE_VIEW_LIST,
  },
  {
    id: 'documents',
    label: 'Tài liệu',
    path: '/documents',
    icon: 'file-text',
    keywords: [
      'documents',
      'tài liệu',
      'tai lieu',
      'files',
      'văn bản',
      'van ban',
      'báo cáo',
      'bao cao',
      'reports',
      'documentation',
    ],
    permission: PermissionEnum.DASHBOARD_VIEW_OWN,
  },
];

/**
 * Lọc menu items dựa trên danh sách quyền
 * @param menuItems Danh sách menu items cần lọc
 * @param permissions Danh sách quyền của người dùng
 * @returns Danh sách menu items mà người dùng có quyền truy cập
 */
export const getMenuItemsByPermissions = (
  menuItems: ModernMenuItem[],
  permissions: PermissionEnum[]
): ModernMenuItem[] => {
  return menuItems.filter(item => permissions.includes(item.permission));
};
