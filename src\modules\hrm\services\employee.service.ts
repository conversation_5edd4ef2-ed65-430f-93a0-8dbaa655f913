import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import {
  AssignRoleDto,
  CreateEmployeeDto,
  CreateEmployeeWithUserDto,
  CreateUserForEmployeeDto,
  EmployeeDto,
  EmployeeQueryDto,
  EmployeeWithUserResponseDto,
  UpdateEmployeeDto,
  UserResponseDto,
} from '../types/employee.types';

/**
 * Service cho các API liên quan đến nhân viên
 */
export const EmployeeService = {
  /**
   * Lấy danh sách nhân viên với phân trang và lọc
   * @param params Tham số truy vấn
   * @returns Danh sách nhân viên đã phân trang
   */
  async getEmployees(
    params?: EmployeeQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<EmployeeDto>>> {
    return apiClient.get<PaginatedResult<EmployeeDto>>('/api/hrm/employees', {
      params,
    });
  },

  /**
   * Lấy chi tiết nhân viên theo ID
   * @param id ID nhân viên
   * @returns Chi tiết nhân viên
   */
  async getEmployee(id: number): Promise<ApiResponseDto<EmployeeDto>> {
    return apiClient.get<EmployeeDto>(`/api/hrm/employees/${id}`);
  },

  /**
   * Lấy nhân viên theo ID người dùng
   * @param userId ID người dùng
   * @returns Chi tiết nhân viên
   */
  async getEmployeeByUserId(userId: number): Promise<ApiResponseDto<EmployeeDto>> {
    return apiClient.get<EmployeeDto>(`/api/hrm/employees/user/${userId}`);
  },

  /**
   * Tạo mới nhân viên
   * @param data Dữ liệu tạo nhân viên
   * @returns Nhân viên đã tạo
   */
  async createEmployee(data: CreateEmployeeDto): Promise<ApiResponseDto<EmployeeDto>> {
    return apiClient.post<EmployeeDto>('/api/hrm/employees', data);
  },

  /**
   * Tạo nhân viên kèm tài khoản người dùng
   * @param data Dữ liệu tạo nhân viên kèm tài khoản người dùng
   * @returns Thông tin nhân viên và tài khoản đã tạo
   */
  async createEmployeeWithUser(
    data: CreateEmployeeWithUserDto
  ): Promise<ApiResponseDto<EmployeeWithUserResponseDto>> {
    return apiClient.post<EmployeeWithUserResponseDto>('/api/hrm/employees/with-user', data);
  },

  /**
   * Tạo tài khoản người dùng cho nhân viên
   * @param data Dữ liệu tạo tài khoản người dùng
   * @returns Thông tin tài khoản đã tạo
   */
  async createUserForEmployee(
    data: CreateUserForEmployeeDto
  ): Promise<ApiResponseDto<UserResponseDto>> {
    return apiClient.post<UserResponseDto>('/api/hrm/employees/with-user/create-user', data);
  },

  /**
   * Cập nhật nhân viên
   * @param id ID nhân viên
   * @param data Dữ liệu cập nhật
   * @returns Nhân viên đã cập nhật
   */
  async updateEmployee(id: number, data: UpdateEmployeeDto): Promise<ApiResponseDto<EmployeeDto>> {
    return apiClient.patch<EmployeeDto>(`/api/hrm/employees/${id}`, data);
  },

  /**
   * Xóa nhân viên
   * @param id ID nhân viên
   * @returns Kết quả xóa
   */
  async deleteEmployee(id: number): Promise<ApiResponseDto<boolean>> {
    return apiClient.delete<boolean>(`/api/hrm/employees/${id}`);
  },

  /**
   * Xóa nhiều nhân viên
   * @param data Dữ liệu xóa nhiều nhân viên
   * @returns Kết quả xóa nhiều
   */
  async bulkDeleteEmployees(data: { ids: number[] }): Promise<
    ApiResponseDto<{
      successCount: number;
      failedCount: number;
      failedIds: number[];
      errors: string[];
    }>
  > {
    return apiClient.delete<{
      successCount: number;
      failedCount: number;
      failedIds: number[];
      errors: string[];
    }>('/api/hrm/employees/bulk-delete/multiple', { data });
  },

  /**
   * Phân quyền cho nhân viên
   * @param id ID nhân viên
   * @param data Dữ liệu phân quyền
   * @returns Kết quả phân quyền
   */
  async assignRole(id: number, data: AssignRoleDto): Promise<ApiResponseDto<EmployeeDto>> {
    return apiClient.patch<EmployeeDto>(`/api/hrm/employees/${id}/roles`, data);
  },

  /**
   * Gán nhân viên vào phòng ban
   * @param id ID nhân viên
   * @param departmentId ID phòng ban
   * @returns Nhân viên đã cập nhật
   */
  async assignToDepartment(id: number, departmentId: number): Promise<ApiResponseDto<EmployeeDto>> {
    return apiClient.patch<EmployeeDto>(`/api/hrm/employees/${id}/department`, { departmentId });
  },

  /**
   * Gán quản lý cho nhân viên
   * @param id ID nhân viên
   * @param managerId ID quản lý
   * @returns Nhân viên đã cập nhật
   */
  async assignManager(id: number, managerId: number): Promise<ApiResponseDto<EmployeeDto>> {
    return apiClient.patch<EmployeeDto>(`/api/hrm/employees/${id}/manager`, { managerId });
  },
};
