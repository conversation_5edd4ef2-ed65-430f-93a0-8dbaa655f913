import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { ObjectiveType } from '../../enum/objective-type.enum';

/**
 * DTO for creating a new objective
 */
export class CreateObjectiveDto {
  /**
   * Title of the objective
   * @example "Tăng doanh thu 20%"
   */
  @ApiProperty({
    description: 'Tiêu đề mục tiêu',
    example: 'Tăng doanh thu 20%',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  /**
   * Detailed description of the objective
   * @example "Tăng doanh thu 20% so với quý trước thông qua các chiến dịch marketing mới"
   */
  @ApiProperty({
    description: 'Mô tả chi tiết mục tiêu',
    example: 'Tăng doanh thu 20% so với quý trước thông qua các chiến dịch marketing mới',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * ID of the user responsible for the objective
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người chịu trách nhiệm',
    example: 1,
  })
  @IsNumber()
  ownerId: number;

  /**
   * ID of the department (if applicable)
   * @example 2
   */
  @ApiProperty({
    description: 'ID của phòng ban (nếu có)',
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  departmentId?: number;

  /**
   * ID of the parent objective (if this is a child objective)
   * @example 3
   */
  @ApiProperty({
    description: 'ID của mục tiêu cha (nếu là mục tiêu con)',
    example: 3,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  parentId?: number;

  /**
   * ID of the OKR cycle
   * @example 1
   */
  @ApiProperty({
    description: 'ID của chu kỳ OKR',
    example: 1,
  })
  @IsNumber()
  cycleId: number;

  /**
   * Type of the objective
   * @example "COMPANY"
   */
  @ApiProperty({
    description: 'Loại mục tiêu',
    enum: ObjectiveType,
    example: ObjectiveType.COMPANY,
  })
  @IsEnum(ObjectiveType)
  type: ObjectiveType;

  /**
   * Start date of the objective
   * @example "2025-01-01"
   */
  @ApiProperty({
    description: 'Ngày bắt đầu mục tiêu',
    example: '2025-01-01',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  /**
   * End date of the objective
   * @example "2025-03-31"
   */
  @ApiProperty({
    description: 'Ngày kết thúc mục tiêu',
    example: '2025-03-31',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;
}
