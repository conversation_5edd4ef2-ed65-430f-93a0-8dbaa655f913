import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { loadEmployeesForAsyncSelect } from '@/modules/hrm/hooks/useEmployees';
import { ObjectiveService } from '@/modules/okrs/services/objective.service';
import { OkrCycleService } from '@/modules/okrs/services/okr-cycle.service';
import { CreateObjectiveDto, ObjectiveType } from '@/modules/okrs/types/objective.types';
import {
  Button,
  Card,
  DatePicker,
  Form,
  FormGrid,
  FormItem,
  Input,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { NotificationUtil } from '@/shared/utils/notification';

export interface CompanyObjectiveFormProps {
  /**
   * Callback khi form được submit thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi form bị đóng
   */
  onCancel?: () => void;

  /**
   * ID của objective cần chỉnh sửa (nếu có)
   */
  objectiveId?: string;

  /**
   * ID của chu kỳ OKR mặc định (nếu có)
   */
  defaultCycleId?: string;
}

/**
 * Form tạo mới hoặc chỉnh sửa Objective cho công ty
 */
const CompanyObjectiveForm: React.FC<CompanyObjectiveFormProps> = ({
  onSuccess,
  onCancel,
  defaultCycleId,
}) => {
  const { t } = useTranslation();
  const formRef = useRef<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Tạo schema validation cho form công ty
  const companyObjectiveSchema = z.object({
    title: z
      .string()
      .min(1, t('okrs:objective.form.validation.titleRequired', 'Tiêu đề không được để trống'))
      .max(
        255,
        t('okrs:objective.form.validation.titleMaxLength', 'Tiêu đề không được vượt quá 255 ký tự')
      ),
    description: z
      .string()
      .max(
        1000,
        t(
          'okrs:objective.form.validation.descriptionMaxLength',
          'Mô tả không được vượt quá 1000 ký tự'
        )
      )
      .optional(),
    cycleId: z.union([z.string(), z.number()], {
      required_error: t(
        'okrs:objective.form.validation.cycleRequired',
        'Chu kỳ OKR không được để trống'
      ),
    }),
    ownerId: z.union([z.string(), z.number()], {
      required_error: t(
        'okrs:objective.form.validation.responsiblePersonRequired',
        'Người chịu trách nhiệm không được để trống'
      ),
    }),

    startDate: z
      .date({
        required_error: t(
          'okrs:objective.form.validation.startDateRequired',
          'Ngày bắt đầu không được để trống'
        ),
      })
      .optional(),
    endDate: z
      .date({
        required_error: t(
          'okrs:objective.form.validation.endDateRequired',
          'Ngày kết thúc không được để trống'
        ),
      })
      .optional(),
  });

  // Load OKR cycles cho AsyncSelectWithPagination
  const loadOkrCyclesForAsyncSelect = async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const { search = '', page = 1, limit = 20 } = params;

      const response = await OkrCycleService.getOkrCycles({
        search: search.trim() || undefined,
        page,
        limit,
      });

      const items = response.result.items.map(cycle => ({
        value: cycle.id,
        label: cycle.name,
        data: {
          startDate: cycle.startDate,
          endDate: cycle.endDate,
          status: cycle.status,
        },
      }));

      return {
        items,
        totalItems: response.result.meta.totalItems,
        totalPages: response.result.meta.totalPages,
        currentPage: response.result.meta.currentPage,
      };
    } catch (error) {
      console.error('Error loading OKR cycles:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }
  };



  // Xử lý khi submit form
  const handleSubmit = async (values: unknown) => {
    try {
      setIsSubmitting(true);
      const formValues = values as {
        title: string;
        description?: string;
        cycleId: string | number;
        ownerId: string | number;
        startDate?: Date;
        endDate?: Date;
      };

      // Chuẩn bị dữ liệu cho API
      const createObjectiveData: CreateObjectiveDto = {
        title: formValues.title,
        description: formValues.description || '',
        type: ObjectiveType.COMPANY,
        cycleId: Number(formValues.cycleId),
        ownerId: Number(formValues.ownerId),

        startDate: formValues.startDate
          ? formValues.startDate.toISOString().split('T')[0]
          : new Date().toISOString().split('T')[0],
        endDate: formValues.endDate
          ? formValues.endDate.toISOString().split('T')[0]
          : new Date().toISOString().split('T')[0],
      };

      // Gọi API tạo objective
      await ObjectiveService.createObjective(createObjectiveData);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('okrs:objective.form.success', 'Tạo mục tiêu công ty thành công!'),
      });

      // Gọi callback onSuccess
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      console.error('Error creating company objective:', error);

      // Hiển thị thông báo lỗi
      const errorMessage =
        error?.response?.data?.message ||
        t('okrs:objective.form.error', 'Có lỗi xảy ra khi tạo mục tiêu');
      NotificationUtil.error({
        message: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý khi hủy form
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  // Set default cycle nếu có
  useEffect(() => {
    if (defaultCycleId && formRef.current) {
      (formRef.current as any).setValue('cycleId', defaultCycleId);
    }
  }, [defaultCycleId]);

  return (
    <Card title={t('okrs:objective.form.companyTitle', 'Mục tiêu mới')} className="mx-auto">
      <Form
        ref={formRef}
        schema={companyObjectiveSchema}
        onSubmit={handleSubmit}
        className="space-y-6"
      >
        <FormItem name="title" label={t('okrs:objective.form.title', 'Tiêu đề')} required>
          <Input
            type="text"
            placeholder={t('okrs:objective.form.titlePlaceholder', 'Thông tin')}
            fullWidth
          />
        </FormItem>

        <FormItem name="description" label={t('okrs:objective.form.description', 'Mô tả')}>
          <Input
            type="textarea"
            placeholder={t('okrs:objective.form.descriptionPlaceholder', 'Thông tin')}
            fullWidth
          />
        </FormItem>

        <FormItem name="cycleId" label={t('okrs:objective.form.cycle', 'Chu kỳ OKR')} required>
          <AsyncSelectWithPagination
            onChange={value => {
              if (formRef.current) {
                (formRef.current as any).setValue('cycleId', value);
              }
            }}
            loadOptions={loadOkrCyclesForAsyncSelect}
            placeholder={t('okrs:objective.form.cyclePlaceholder', 'Chọn chu kỳ OKR')}
            debounceTime={300}
            noOptionsMessage={t('common:noResults', 'Không tìm thấy chu kỳ OKR')}
            loadingMessage={t('common:loading', 'Đang tìm kiếm...')}
            autoLoadInitial={true}
            itemsPerPage={10}
            fullWidth
          />
        </FormItem>

        <FormItem
          name="ownerId"
          label={t('okrs:objective.form.responsiblePerson', 'Người chịu trách nhiệm')}
          required
        >
          <AsyncSelectWithPagination
            onChange={value => {
              if (formRef.current) {
                (formRef.current as any).setValue('ownerId', value);
              }
            }}
            loadOptions={loadEmployeesForAsyncSelect}
            placeholder={t(
              'okrs:objective.form.responsiblePersonPlaceholder',
              'Chọn người chịu trách nhiệm'
            )}
            debounceTime={300}
            noOptionsMessage={t('common:noResults', 'Không tìm thấy nhân viên')}
            loadingMessage={t('common:loading', 'Đang tìm kiếm...')}
            autoLoadInitial={true}
            itemsPerPage={10}
            fullWidth
          />
        </FormItem>


        <FormGrid columns={2} gap="md">
          <FormItem name="startDate" label={t('okrs:objective.form.startDate', 'Ngày bắt đầu')}>
            <DatePicker
              placeholder={t('okrs:objective.form.startDatePlaceholder', 'Chọn ngày bắt đầu')}
              fullWidth
            />
          </FormItem>

          <FormItem name="endDate" label={t('okrs:objective.form.endDate', 'Ngày kết thúc')}>
            <DatePicker
              placeholder={t('okrs:objective.form.endDatePlaceholder', 'Chọn ngày kết thúc')}
              fullWidth
            />
          </FormItem>
        </FormGrid>

        <div className="flex justify-end space-x-3 pt-4">
          <Button variant="outline" onClick={handleCancel} disabled={isSubmitting}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button type="submit" isLoading={isSubmitting}>
            {t('okrs:objective.form.submit', 'Tạo mục tiêu')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default CompanyObjectiveForm;
