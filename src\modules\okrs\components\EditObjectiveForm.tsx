import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { useAuth } from '@/modules/auth';
import { ObjectiveService } from '@/modules/okrs/services/objective.service';
import { OkrCycleService } from '@/modules/okrs/services/okr-cycle.service';
import { ObjectiveDto, ObjectiveType, UpdateObjectiveDto } from '@/modules/okrs/types/objective.types';
import {
  Button,
  Card,
  DatePicker,
  Form,
  FormGrid,
  FormItem,
  Input,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { NotificationUtil } from '@/shared/utils/notification';
import { useFormErrors } from '@/shared/hooks/useFormErrors';

export interface EditObjectiveFormProps {
  /**
   * Objective cần chỉnh sửa
   */
  objective: ObjectiveDto;

  /**
   * Callback khi form được submit thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi form bị đóng
   */
  onCancel?: () => void;
}

/**
 * Form chỉnh sửa Objective cá nhân
 */
const EditObjectiveForm: React.FC<EditObjectiveFormProps> = ({ 
  objective, 
  onSuccess, 
  onCancel 
}) => {
  const { t } = useTranslation();
  const formRef = useRef<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { formRef: errorFormRef, setFormErrors } = useFormErrors<{
    title: string;
    description?: string;
    cycleId: string | number;
    startDate?: Date;
    endDate?: Date;
  }>();

  // Schema validation cho form
  const objectiveSchema = z.object({
    title: z.string().min(1, {
      message: t(
        'okrs:objective.form.validation.titleRequired',
        'Tiêu đề không được để trống'
      ),
    }),
    description: z.string().optional(),
    cycleId: z.union([z.string(), z.number()]).refine(val => val !== '' && val !== null, {
      message: t(
        'okrs:objective.form.validation.cycleRequired',
        'Chu kỳ OKR không được để trống'
      ),
    }),
    startDate: z
      .date({
        required_error: t(
          'okrs:objective.form.validation.startDateRequired',
          'Ngày bắt đầu không được để trống'
        ),
      })
      .optional(),
    endDate: z
      .date({
        required_error: t(
          'okrs:objective.form.validation.endDateRequired',
          'Ngày kết thúc không được để trống'
        ),
      })
      .optional(),
  });

  // Load OKR cycles function cho AsyncSelectWithPagination
  const loadCycleOptions = async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const { search = '', page = 1, limit = 20 } = params;

      const response = await OkrCycleService.getOkrCycles({
        search: search.trim() || undefined,
        page,
        limit,
      });

      const items = response.result.items.map(cycle => ({
        value: cycle.id,
        label: cycle.name,
        data: cycle,
      }));

      return {
        items,
        totalItems: response.result.meta.totalItems,
        totalPages: response.result.meta.totalPages,
        currentPage: response.result.meta.currentPage,
      };
    } catch (error) {
      console.error('Error loading cycles:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }
  };

  // Lấy thông tin người dùng hiện tại
  const { user } = useAuth();

  // Xử lý khi submit form
  const handleSubmit = async (values: unknown) => {
    try {
      setIsSubmitting(true);
      const formValues = values as {
        title: string;
        description?: string;
        cycleId: string | number;
        startDate?: Date;
        endDate?: Date;
      };

      // Chuẩn bị dữ liệu cho API
      const updateObjectiveData: UpdateObjectiveDto = {
        title: formValues.title,
        description: formValues.description || '',
        cycleId: Number(formValues.cycleId),
        startDate: formValues.startDate
          ? formValues.startDate.toISOString().split('T')[0]
          : undefined,
        endDate: formValues.endDate
          ? formValues.endDate.toISOString().split('T')[0]
          : undefined,
      };

      // Gọi API cập nhật objective
      await ObjectiveService.updateObjective(objective.id, updateObjectiveData);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('okrs:objective.form.updateSuccess', 'Cập nhật mục tiêu thành công'),
      });

      // Gọi callback onSuccess nếu có
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      // Xử lý lỗi
      console.error('Error updating objective:', error);
      NotificationUtil.error({
        message: t('okrs:objective.form.updateError', 'Có lỗi xảy ra khi cập nhật mục tiêu'),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý khi nhấn nút hủy
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  // Thiết lập giá trị mặc định cho form từ objective hiện tại
  useEffect(() => {
    if (formRef.current && objective) {
      formRef.current.setValue('title', objective.title);
      formRef.current.setValue('description', objective.description || '');
      formRef.current.setValue('cycleId', objective.cycleId);
      
      if (objective.startDate) {
        formRef.current.setValue('startDate', new Date(objective.startDate));
      }
      if (objective.endDate) {
        formRef.current.setValue('endDate', new Date(objective.endDate));
      }
    }
  }, [objective, formRef]);

  return (
    <Card title={t('okrs:objective.form.editIndividualTitle', 'Chỉnh sửa mục tiêu cá nhân')} className="mx-auto">
      <Form ref={formRef} schema={objectiveSchema} onSubmit={handleSubmit} className="space-y-6">
        <FormItem name="title" label={t('okrs:objective.form.title', 'Tiêu đề')} required>
          <Input
            type="text"
            placeholder={t('okrs:objective.form.titlePlaceholder', 'Thông tin')}
            fullWidth
          />
        </FormItem>

        <FormItem name="description" label={t('okrs:objective.form.description', 'Mô tả')}>
          <Input
            type="textarea"
            placeholder={t('okrs:objective.form.descriptionPlaceholder', 'Thông tin')}
            fullWidth
          />
        </FormItem>

        <FormItem name="cycleId" label={t('okrs:objective.form.cycle', 'Chu kỳ OKR')} required>
          <AsyncSelectWithPagination
            onChange={value => {
              if (formRef.current) {
                (formRef.current as any).setValue('cycleId', value);
              }
            }}
            loadOptions={loadCycleOptions}
            placeholder={t('okrs:objective.form.cyclePlaceholder', 'Chọn chu kỳ OKR')}
            fullWidth
            debounceTime={300}
            itemsPerPage={20}
            noOptionsMessage={t('okrs:objective.form.noCycles', 'Không tìm thấy chu kỳ OKR')}
            loadingMessage={t('common:loading', 'Đang tải...')}
            autoLoadInitial={true}
          />
        </FormItem>

        <FormGrid columns={2} gap="md">
          <FormItem name="startDate" label={t('okrs:objective.form.startDate', 'Ngày bắt đầu')}>
            <DatePicker
              placeholder={t('okrs:objective.form.startDatePlaceholder', 'Chọn ngày bắt đầu')}
              fullWidth
            />
          </FormItem>

          <FormItem name="endDate" label={t('okrs:objective.form.endDate', 'Ngày kết thúc')}>
            <DatePicker
              placeholder={t('okrs:objective.form.endDatePlaceholder', 'Chọn ngày kết thúc')}
              fullWidth
            />
          </FormItem>
        </FormGrid>

        <div className="flex justify-end space-x-3 pt-4">
          <Button variant="outline" onClick={handleCancel} disabled={isSubmitting}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button type="submit" isLoading={isSubmitting}>
            {t('okrs:objective.form.update', 'Cập nhật mục tiêu')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default EditObjectiveForm;
