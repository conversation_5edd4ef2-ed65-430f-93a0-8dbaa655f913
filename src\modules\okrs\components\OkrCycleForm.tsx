import React from 'react';
import { Controller, FieldValues, SubmitHandler } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
  Select,
  Typography,
} from '@/shared/components/common';
import { DatePickerFormField } from '@/shared/components/common/DatePicker/DatePicker';
import { NotificationUtil } from '@/shared/utils/notification';

import { useCreateOkrCycle, useUpdateOkrCycle } from '../hooks/useOkrCycles';
import {
  CreateOkrCycleDto,
  OkrCycleResponseDto,
  OkrCycleStatus,
  UpdateOkrCycleDto,
} from '../types/okr-cycle.types';

interface OkrCycleFormProps {
  /**
   * Chu kỳ OKR cần cập nhật (nếu có)
   */
  cycle?: OkrCycleResponseDto;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi submit form thành công
   */
  onSuccess?: () => void;

  /**
   * Tiêu đề form
   */
  title?: string;
}

// Định nghĩa interface cho dữ liệu form
interface CycleFormValues {
  name: string;
  startDate: string;
  endDate: string;
  status?: OkrCycleStatus;
}

/**
 * Form tạo/cập nhật chu kỳ OKR
 */
const OkrCycleForm: React.FC<OkrCycleFormProps> = ({ cycle, onClose, onSuccess, title }) => {
  const { t } = useTranslation(['okrs', 'common']);
  const isEditMode = !!cycle;

  // Mutations
  const createCycleMutation = useCreateOkrCycle();
  const updateCycleMutation = useUpdateOkrCycle();

  // Định nghĩa schema validation
  const cycleSchema = z
    .object({
      name: z.string().min(1, t('common:validation.required', 'Trường này là bắt buộc')),
      startDate: z.string().min(1, t('common:validation.required', 'Trường này là bắt buộc')),
      endDate: z.string().min(1, t('common:validation.required', 'Trường này là bắt buộc')),
      status: z.nativeEnum(OkrCycleStatus).optional(),
    })
    .refine(
      data => {
        // Kiểm tra startDate < endDate
        if (!data.startDate || !data.endDate) {
          return true;
        }
        return new Date(data.startDate) < new Date(data.endDate);
      },
      {
        message: t('okrs:cycle.validation.dateRange', 'Ngày bắt đầu phải trước ngày kết thúc'),
        path: ['endDate'],
      }
    );

  // Giá trị mặc định
  const defaultValues: CycleFormValues = {
    name: cycle?.name || '',
    startDate: cycle?.startDate || '',
    endDate: cycle?.endDate || '',
    status: cycle?.status || OkrCycleStatus.PLANNING,
  };

  // Options cho select status
  const statusOptions = [
    { value: OkrCycleStatus.PLANNING, label: t('okrs:cycle.status.planning', 'Lập kế hoạch') },
    { value: OkrCycleStatus.ACTIVE, label: t('okrs:cycle.status.active', 'Đang hoạt động') },
    { value: OkrCycleStatus.CLOSED, label: t('okrs:cycle.status.closed', 'Đã đóng') },
  ];

  // Xử lý submit form
  const handleSubmit = async (values: CycleFormValues) => {
    try {
      if (isEditMode && cycle) {
        // Cập nhật chu kỳ
        const updateData: UpdateOkrCycleDto = {
          name: values.name,
          startDate: values.startDate,
          endDate: values.endDate,
          status: values.status,
        };

        await updateCycleMutation.mutateAsync({ id: cycle.id, data: updateData });
        NotificationUtil.success({
          message: t('okrs:cycle.messages.updateSuccess', 'Cập nhật chu kỳ OKR thành công'),
        });
      } else {
        // Tạo chu kỳ mới
        const createData: CreateOkrCycleDto = {
          name: values.name,
          startDate: values.startDate,
          endDate: values.endDate,
          status: values.status,
        };

        await createCycleMutation.mutateAsync(createData);
        NotificationUtil.success({
          message: t('okrs:cycle.messages.createSuccess', 'Tạo chu kỳ OKR thành công'),
        });
      }

      // Gọi callback thành công và đóng form
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
      NotificationUtil.error({
        message: isEditMode
          ? t('okrs:cycle.messages.updateError', 'Cập nhật chu kỳ OKR thất bại')
          : t('okrs:cycle.messages.createError', 'Tạo chu kỳ OKR thất bại'),
      });
    }
  };



  return (
    <Card className="p-4">
      <Typography variant="h5" className="mb-4">
        {title ||
          (isEditMode
            ? t('okrs:cycle.form.editTitle', 'Cập nhật chu kỳ OKR')
            : t('okrs:cycle.form.title', 'Thêm chu kỳ OKR mới'))}
      </Typography>

      <Form
        schema={cycleSchema}
        defaultValues={defaultValues}
        onSubmit={handleSubmit as unknown as SubmitHandler<FieldValues>}
        className="space-y-4"
      >
        {/* Tên chu kỳ */}
        <FormItem name="name" label={t('okrs:cycle.table.name', 'Tên chu kỳ')} required>
          <Input
            placeholder={t('okrs:cycle.form.namePlaceholder', 'Nhập tên chu kỳ (vd: Q1-2025)')}
            fullWidth
          />
        </FormItem>

        {/* Ngày bắt đầu */}
        <FormItem name="startDate" label={t('okrs:cycle.table.startDate', 'Ngày bắt đầu')} required>
          <Controller
            name="startDate"
            render={({ field }) => (
              <DatePickerFormField
                placeholder={t('okrs:cycle.form.startDatePlaceholder', 'Chọn ngày bắt đầu')}
                value={field.value}
                onChange={field.onChange}
                fullWidth
              />
            )}
          />
        </FormItem>

        {/* Ngày kết thúc */}
        <FormItem name="endDate" label={t('okrs:cycle.table.endDate', 'Ngày kết thúc')} required>
          <Controller
            name="endDate"
            render={({ field }) => (
              <DatePickerFormField
                placeholder={t('okrs:cycle.form.endDatePlaceholder', 'Chọn ngày kết thúc')}
                value={field.value}
                onChange={field.onChange}
                fullWidth
              />
            )}
          />
        </FormItem>

        {/* Trạng thái */}
        <FormItem name="status" label={t('okrs:cycle.table.status', 'Trạng thái')}>
          <Select
            options={statusOptions}
            placeholder={t('okrs:cycle.form.statusPlaceholder', 'Chọn trạng thái')}
            fullWidth
          />
        </FormItem>

        {/* Buttons */}
        <div className="flex justify-end space-x-3 pt-4">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={createCycleMutation.isPending || updateCycleMutation.isPending}
          >
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={createCycleMutation.isPending || updateCycleMutation.isPending}
          >
            {isEditMode ? t('common:update', 'Cập nhật') : t('common:save', 'Lưu')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default OkrCycleForm;
